# USER ROLE & PERMISSION MATRIX
## Dual-Version Calculator System

---

## 🎭 USER ROLES OVERVIEW

### **Three-Tier Access System**
1. **Public Users** - Anonymous access to Tax Torpedo calculator
2. **Agents** - Authenticated professionals with full calculator access
3. **Admins** - System administrators with total control

---

## 📊 COMPREHENSIVE PERMISSION MATRIX

### **CALCULATION ACCESS**

| Feature | Public | Agent | Admin | Notes |
|---------|--------|-------|-------|-------|
| **Tax Torpedo Calculation** | ✅ | ✅ | ✅ | `currentSavings * 0.3` |
| **Pension Gap Calculation** | ❌ | ✅ | ✅ | `currentPension * 0.03 * yearsOfService` |
| **Survivor Gap Calculation** | ❌ | ✅ | ✅ | `currentPension * 0.4` |
| **Risk Score Calculation** | ❌ | ✅ | ✅ | Full risk assessment |
| **Hidden Benefit Opportunity** | ❌ | ✅ | ✅ | Professional analysis |

### **DATA ACCESS & MANAGEMENT**

| Feature | Public | Agent | Admin | Scope |
|---------|--------|-------|-------|-------|
| **View Own Sessions** | ❌ | ✅ | ✅ | Agent: Own only, Admin: All |
| **Create Calculation Sessions** | ✅ | ✅ | ✅ | Public: Anonymous only |
| **Edit Session Data** | ❌ | ✅ | ✅ | Agent: Own only, Admin: All |
| **Delete Sessions** | ❌ | ❌ | ✅ | Admin only |
| **Export Session Data** | ❌ | ✅ | ✅ | Agent: Own only, Admin: All |
| **View Client Information** | ❌ | ✅ | ✅ | Agent: Own clients, Admin: All |

### **USER MANAGEMENT**

| Feature | Public | Agent | Admin | Details |
|---------|--------|-------|-------|---------|
| **Register as Agent** | ✅ | ❌ | ❌ | Public can apply |
| **View Own Profile** | ❌ | ✅ | ✅ | |
| **Edit Own Profile** | ❌ | ✅ | ✅ | |
| **View All Agent Profiles** | ❌ | ❌ | ✅ | Admin only |
| **Edit Any Agent Profile** | ❌ | ❌ | ✅ | Admin only |
| **Approve/Reject Agents** | ❌ | ❌ | ✅ | Admin only |
| **Deactivate Agents** | ❌ | ❌ | ✅ | Admin only |
| **Create Agent Accounts** | ❌ | ❌ | ✅ | Manual creation |

### **SYSTEM ADMINISTRATION**

| Feature | Public | Agent | Admin | Purpose |
|---------|--------|-------|-------|---------|
| **View System Analytics** | ❌ | ❌ | ✅ | Usage metrics, performance |
| **Access Audit Logs** | ❌ | ❌ | ✅ | Security monitoring |
| **Manage System Settings** | ❌ | ❌ | ✅ | Global configuration |
| **Edit Calculation Parameters** | ❌ | ❌ | ✅ | Future feature |
| **Manage Email Templates** | ❌ | ❌ | ✅ | Communication templates |
| **Security Configuration** | ❌ | ❌ | ✅ | Password policies, etc. |

### **REPORTING & COMMUNICATION**

| Feature | Public | Agent | Admin | Capabilities |
|---------|--------|-------|-------|-------------|
| **Generate Basic Reports** | ❌ | ✅ | ✅ | Agent: Own clients |
| **Generate Branded Reports** | ❌ | ✅ | ✅ | Agent branding |
| **Access Report Templates** | ❌ | ✅ | ✅ | Customizable templates |
| **Submit Contact Requests** | ✅ | ❌ | ❌ | Lead generation |
| **View Contact Requests** | ❌ | ✅ | ✅ | Agent: Assigned, Admin: All |
| **Assign Contact Requests** | ❌ | ❌ | ✅ | Admin assignment |

---

## 🔐 DETAILED ACCESS CONTROL SPECIFICATIONS

### **PUBLIC USER CAPABILITIES**

#### **Allowed Actions**
- Access public calculator interface
- Input basic demographic information (profession, age, state, savings)
- Calculate Tax Torpedo amount only
- View public results with CTA messaging
- Submit contact form for consultation
- Create anonymous calculation sessions

#### **Restricted Actions**
- Cannot access pension gap or survivor gap calculations
- Cannot view years of service or pension estimate fields
- Cannot save or retrieve calculation sessions
- Cannot access any authenticated areas
- Cannot view other users' data

#### **Data Collection**
```javascript
// Public user data collected
{
  profession: "teacher|nurse|first-responder|state-local-hero",
  currentSavings: number,
  state: "state_code",
  currentAge: number,
  retirementAge: number,
  // Hidden from public
  yearsOfService: undefined,
  pensionEstimate: undefined,
  inflationProtection: undefined,
  survivorPlanning: undefined,
  financialFears: undefined
}
```

### **AGENT USER CAPABILITIES**

#### **Authentication Requirements**
- Must register and await admin approval
- Secure login with email/password
- Session management with auto-refresh
- Role verification on each request

#### **Profile Management**
```javascript
// Agent customizable profile data
{
  personalInfo: {
    firstName: "string",
    lastName: "string",
    credentials: "string",
    licenseNumber: "string",
    bio: "text",
    yearsExperience: number
  },
  branding: {
    logoUrl: "url",
    brandPrimaryColor: "#hex",
    brandSecondaryColor: "#hex",
    companyWebsite: "url",
    officeAddress: "text",
    officeHours: "text"
  },
  contactPreferences: {
    preferredMethod: "email|phone|text",
    responseTime: "immediate|24_hours|48_hours",
    availableDays: ["monday", "tuesday", ...]
  },
  customMessaging: {
    welcomeMessage: "text",
    signature: "text",
    disclaimer: "text"
  }
}
```

#### **Client Management Capabilities**
- View all own client calculation sessions
- Add notes and follow-up reminders
- Export client data and reports
- Generate branded professional reports
- Track client interaction history
- Search and filter client sessions

#### **Calculation Access**
- Full access to all three gap calculations
- Complete risk assessment tools
- Professional analysis features
- Custom report generation
- Client data persistence

### **ADMIN USER CAPABILITIES**

#### **Total System Control**
- Complete administrative control over entire application
- Access to all user data and system functions
- Ability to override any system setting
- Full audit trail access

#### **Agent Management**
```javascript
// Admin agent management capabilities
{
  registration: {
    review: "View all pending applications",
    approve: "Approve agent applications",
    reject: "Reject with reason",
    manual_create: "Create agent accounts directly"
  },
  management: {
    view_all: "Access all agent profiles",
    edit_any: "Modify any agent information",
    status_control: "Activate/deactivate/suspend",
    bulk_operations: "Mass approve/reject/modify"
  },
  monitoring: {
    activity_tracking: "Monitor agent usage",
    performance_metrics: "Agent performance data",
    client_volume: "Sessions per agent",
    conversion_rates: "Lead to client conversion"
  }
}
```

#### **System Analytics Access**
- Real-time usage metrics
- Conversion tracking and analysis
- Performance monitoring
- Security event monitoring
- Financial reporting capabilities

#### **Future Administrative Features**
- Calculation parameter editing
- Formula customization
- System-wide configuration changes
- Advanced security settings
- Custom reporting tools

---

## 🛡️ SECURITY IMPLEMENTATION

### **Row Level Security (RLS) Policies**

#### **Agents Table**
```sql
-- Agents can view/edit own profile
CREATE POLICY "agents_own_profile" ON agents
  FOR ALL USING (auth.uid() = auth_user_id);

-- Admins can view/edit all agents
CREATE POLICY "admins_all_agents" ON agents
  FOR ALL USING (
    EXISTS (SELECT 1 FROM admin_users WHERE auth_user_id = auth.uid())
  );
```

#### **Client Sessions Table**
```sql
-- Agents can access own sessions
CREATE POLICY "agents_own_sessions" ON client_sessions
  FOR ALL USING (
    agent_id IN (SELECT id FROM agents WHERE auth_user_id = auth.uid())
  );

-- Public can create anonymous sessions
CREATE POLICY "public_create_sessions" ON client_sessions
  FOR INSERT WITH CHECK (session_type = 'public' AND agent_id IS NULL);
```

### **Authentication Flow Security**
1. **Registration**: Email verification required
2. **Approval**: Admin manual approval process
3. **Login**: Secure JWT token management
4. **Session**: Auto-refresh with secure storage
5. **Logout**: Complete session cleanup

### **Data Protection Measures**
- All sensitive data encrypted at rest
- Input validation and sanitization
- Rate limiting on public endpoints
- Audit logging for all admin actions
- Regular security monitoring

---

## 📋 IMPLEMENTATION CHECKLIST

### **Public Version Security**
- [ ] Tax Torpedo calculation only
- [ ] No access to sensitive fields
- [ ] Anonymous session creation
- [ ] Contact form rate limiting
- [ ] Input validation and sanitization

### **Agent Version Security**
- [ ] Secure authentication required
- [ ] Profile approval workflow
- [ ] Data isolation between agents
- [ ] Session management
- [ ] Branded report generation

### **Admin Version Security**
- [ ] Enhanced authentication
- [ ] Complete system access
- [ ] Audit trail logging
- [ ] Security monitoring
- [ ] Backup and recovery

### **Database Security**
- [ ] RLS policies implemented
- [ ] Role-based access control
- [ ] Data encryption at rest
- [ ] Regular security audits
- [ ] Backup and disaster recovery

---

*This permission matrix ensures secure, role-based access control while maintaining the required functionality separation between public and authenticated users.*
